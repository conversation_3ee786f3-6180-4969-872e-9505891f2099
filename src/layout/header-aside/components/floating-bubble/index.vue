<template>
  <div>
    <!-- 可拖动气泡 -->
    <div
        class="floating-bubble"
        :style="{left: bubblePosition.x + 'px', top: bubblePosition.y + 'px'}"
        @mousedown="startDrag"
        @click="openModal">
      <d2-icon name="comment" />
    </div>

    <!-- 自定义模态框 - 不使用el-dialog -->
    <div class="custom-modal" v-show="modalVisible">
      <!-- 半透明背景 -->
      <div class="modal-overlay"></div>

      <!-- 模态框内容 -->
      <div
          class="modal-content"
          :class="{'modal-content-fullscreen': isMobile}"
          ref="modalContent">
        <!-- 关闭按钮 -->
        <div class="close-button" @click="closeModal">
          <d2-icon name="times" />
        </div>

        <!-- iframe - 始终存在，只是显示/隐藏 -->
        <iframe
            src="https://chat.marsmind.co"
            frameborder="0"
            scrolling="auto"
            class="chat-iframe"
            referrerpolicy="no-referrer"
            loading="lazy"
            allow="microphone; camera">
        </iframe>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations } from 'vuex'

export default {
  name: 'FloatingBubble',
  data() {
    return {
      // 拖拽状态
      isDragging: false,
      dragOffset: {
        x: 0,
        y: 0
      },
      // 模态框状态
      modalVisible: false,
      // 是否移动设备
      isMobile: window.innerWidth < 768
    }
  },
  computed: {
    ...mapState('d2admin/bubble', [
      'bubblePosition'
    ])
  },
  mounted() {
    // 添加全局鼠标事件监听
    document.addEventListener('mousemove', this.onDrag)
    document.addEventListener('mouseup', this.stopDrag)
    // 添加窗口大小变化监听
    window.addEventListener('resize', this.handleResize)
    // 添加ESC键关闭监听
    document.addEventListener('keydown', this.handleKeyDown)

    // 确保气泡在可视区域内
    this.ensureInViewport()
  },
  beforeDestroy() {
    // 清理事件监听
    document.removeEventListener('mousemove', this.onDrag)
    document.removeEventListener('mouseup', this.stopDrag)
    window.removeEventListener('resize', this.handleResize)
    document.removeEventListener('keydown', this.handleKeyDown)
  },
  methods: {
    ...mapMutations('d2admin/bubble', [
      'setBubblePosition'
    ]),

    // 处理ESC键
    handleKeyDown(e) {
      if (e.key === 'Escape' && this.modalVisible) {
        this.closeModal()
      }
    },

    // 开始拖拽
    startDrag(e) {
      e.preventDefault()
      this.isDragging = true
      this.dragOffset.x = e.clientX - this.bubblePosition.x
      this.dragOffset.y = e.clientY - this.bubblePosition.y
    },

    // 拖拽中
    onDrag(e) {
      if (this.isDragging) {
        const newPosition = {
          x: e.clientX - this.dragOffset.x,
          y: e.clientY - this.dragOffset.y
        }

        // 限制在可视区域内
        const bubbleSize = 48
        if (newPosition.x < 0) newPosition.x = 0
        if (newPosition.y < 0) newPosition.y = 0
        if (newPosition.x > window.innerWidth - bubbleSize) {
          newPosition.x = window.innerWidth - bubbleSize
        }
        if (newPosition.y > window.innerHeight - bubbleSize) {
          newPosition.y = window.innerHeight - bubbleSize
        }

        // 更新到Vuex
        this.setBubblePosition(newPosition)
      }
    },

    // 停止拖拽
    stopDrag() {
      this.isDragging = false
    },

    // 确保气泡在可视区域内
    ensureInViewport() {
      const bubbleSize = 48 // 气泡大小
      let needUpdate = false
      const newPosition = { ...this.bubblePosition }

      // 限制X轴位置
      if (newPosition.x < 0) {
        newPosition.x = 0
        needUpdate = true
      } else if (newPosition.x > window.innerWidth - bubbleSize) {
        newPosition.x = window.innerWidth - bubbleSize
        needUpdate = true
      }

      // 限制Y轴位置
      if (newPosition.y < 0) {
        newPosition.y = 0
        needUpdate = true
      } else if (newPosition.y > window.innerHeight - bubbleSize) {
        newPosition.y = window.innerHeight - bubbleSize
        needUpdate = true
      }

      // 如果有更新，保存到Vuex
      if (needUpdate) {
        this.setBubblePosition(newPosition)
      }
    },

    // 打开模态框
    openModal() {
      if (!this.isDragging) {
        this.modalVisible = true
        document.body.classList.add('modal-open') // 防止背景滚动
      }
    },

    // 关闭模态框
    closeModal() {
      this.modalVisible = false
      document.body.classList.remove('modal-open')
    },

    // 处理窗口大小变化
    handleResize() {
      this.isMobile = window.innerWidth < 768
      this.ensureInViewport()
    }
  }
}
</script>

<style lang="scss" scoped>
.floating-bubble {
  position: fixed;
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #0d47a1;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  z-index: 9999;
  transition: box-shadow 0.3s;

  &:hover {
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.5);
  }

  i {
    font-size: 24px;
  }
}

/* 自定义模态框样式 */
.custom-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
  position: relative;
  width: 80%;
  height: 70vh;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.3);
  overflow: hidden;
  z-index: 10001;

  &-fullscreen {
    width: 100%;
    height: 100%;
    border-radius: 0;
  }
}

.chat-iframe {
  width: 97%;
  height: 100%;
  border: none;
  display: block;
  background-color: white;
  isolation: isolate;
  margin: 0 auto;
}

.close-button {
  position: absolute;
  top: 23px;
  right: 10px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background-color: #0d47a1;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10002;

  &:hover {
    background-color: rgba(0, 0, 0, 0.5);
  }
}

// 为移动设备优化
@media (max-width: 768px) {
  .modal-content {
    height: 80vh;
  }
}
</style>

<style lang="scss">
/* 全局样式 */
body.modal-open {
  overflow: hidden;
}
</style>
